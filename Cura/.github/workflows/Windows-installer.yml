# 名称：在 GitHub Actions 页面显示的任务名称
name: Build and Package Cura Installer (Windows)

# 触发方式：手动触发 workflow（可选 future 扩展 push/tag 等触发）
on:
  workflow_dispatch:

jobs:
  build-windows:
    # 指定运行平台为 GitHub 提供的最新 Windows 系统（Windows Server 2022）
    runs-on: windows-latest

    # 设置一些环境变量
    env:
      CURA_VERSION: 5.11.0  # 指定 Cura 的版本号
      VENV_DIR: ${{ github.workspace }}\.venv  # Python 虚拟环境目录
      CONAN_USER_HOME: ${{ github.workspace }}\.conan  # Conan 本地缓存目录

    steps:
    # 第一步：检出你当前 GitHub 仓库的代码（即 wsd07/Cura）
    - name: 🧾 检出 Cura 源码
      uses: actions/checkout@v3

    # 第二步：安装 Python 3.12（Conan 2.x 需要 Python ≥ 3.8，推荐最新版）
    - name: 🐍 安装 Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    # 第三步：安装 CMake、Ninja、NSIS、7zip、Visual Studio 构建工具等依赖
    - name: 🛠 安装构建工具
      run: |
        choco install cmake --version=3.27.0 -y  # 安装 CMake，确保版本 ≥3.23
        choco install ninja -y                  # 安装 Ninja，用于高效构建
        choco install nsis -y                   # 安装 NSIS，用于生成安装包
        choco install 7zip -y                   # 安装 7-Zip，打包或解压
        choco install visualstudio2022buildtools --package-parameters "--add Microsoft.VisualStudio.Workload.VCTools" -y
        choco install windows-sdk-10.1 -y       # 安装 Windows SDK 解决 DLL 缺失问题
        # 将 NSIS 添加到 PATH
        $nsisPath = "${env:ProgramFiles(x86)}\NSIS"
        echo "NSIS路径: $nsisPath"
        echo "$nsisPath" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

    # 第四步：创建虚拟环境并安装 Python 依赖，包括 Conan 2.x、sip 等
    - name: 🧪 安装 Python 依赖
      run: |
        python -m venv $env:VENV_DIR  # 创建虚拟环境
        . "$env:VENV_DIR\Scripts\Activate.ps1"  # 激活虚拟环境
        python -m pip install --upgrade pip
        pip install sip==6.5.1  # 安装官方要求版本的 sip
        pip install conan==2.7.0  # 安装 Conan 2.x（注意不要用旧版）
        pip install gitpython
        conan config install https://github.com/wsd07/conan-config.git  # ✅ 拉取你的共享配置
        conan profile detect --force  # ✅ 自动生成默认 profile

    # 第五步：克隆 Uranium 和 CuraEngine 仓库源码
    - name: 🔃 克隆 Uranium 和 CuraEngine
      run: |
        git clone https://github.com/wsd07/Uranium.git ../Uranium
        git clone https://github.com/wsd07/CuraEngine.git ../CuraEngine

    # 第六步：使用 conan editable 模式注册本地源码（新版语法）
    - name: 🔗 注册 editable 源码依赖（Conan 2.x 正确语法）
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        conan editable add ../Uranium --name=uranium --version=5.11.0 --user=wsd07 --channel=testing
        conan editable add ../CuraEngine --name=curaengine --version=5.11.0 --user=wsd07 --channel=testing

    # 第七步：创建构建目录
    - name: 🏗 创建构建目录
      run: mkdir build

    # 第八步：创建 Cura 包并使用 deploy() 方法
    - name: 📦 创建 Cura 包并部署
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1

        # 创建 Cura 包
        conan create . --build=missing -s build_type=Release -c tools.cmake.cmaketoolchain:generator=Ninja

        # 创建部署目录
        mkdir -p dist

        # 使用 conan install 触发 conanfile.py 中的 deploy() 方法
        # 在 Conan 2.x 中，使用正确的 --deployer-package 语法
        conan install . -s build_type=Release --deployer-package="*" --deployer-folder="$env:GITHUB_WORKSPACE/dist"

        # 检查是否成功部署
        Write-Host "=== 检查 deploy() 方法的输出 ==="
        if (Test-Path "dist/venv") {
            Write-Host "✅ 虚拟环境已创建"
            Get-ChildItem "dist/venv" | ForEach-Object { Write-Host "  - $($_.Name)" }
        } else {
            Write-Error "❌ deploy() 方法未创建虚拟环境"
            Write-Host "dist 目录内容："
            Get-ChildItem -Recurse dist
            exit 1
        }

        # 检查 PyInstaller spec 文件
        if (Test-Path "dist/UltiMaker-Cura.spec") {
            Write-Host "✅ PyInstaller spec 文件已生成"
        } else {
            Write-Error "❌ PyInstaller spec 文件未生成"
            exit 1
        }



    # 第十步：使用 deploy() 创建的虚拟环境运行 PyInstaller
    - name: 🔨 使用 PyInstaller 构建可执行文件
      run: |
        # 激活 deploy() 方法创建的虚拟环境
        # 这个环境包含所有 Cura 依赖
        $deploy_venv = "$env:GITHUB_WORKSPACE/dist/venv"
        if (Test-Path "$deploy_venv/Scripts/activate.ps1") {
            . "$deploy_venv/Scripts/activate.ps1"
            Write-Host "✅ 已激活 deploy() 创建的虚拟环境"
        } else {
            Write-Error "❌ 未找到 deploy() 创建的虚拟环境"
            exit 1
        }

        # 安装 PyInstaller 到 deploy 环境中
        pip install pyinstaller==6.3.0

        # 切换到部署目录并运行 pyinstaller
        cd dist
        pyinstaller UltiMaker-Cura.spec --clean --noconfirm --log-level=INFO

        # 验证可执行文件生成
        if (-not (Test-Path "dist/UltiMaker-Cura/UltiMaker-Cura.exe")) {
            Write-Error "❌ PyInstaller 未生成可执行文件"
            Write-Host "PyInstaller 输出目录内容："
            Get-ChildItem -Recurse
            exit 1
        }

        $exe_size = (Get-Item "dist/UltiMaker-Cura/UltiMaker-Cura.exe").Length/1MB
        Write-Host "✅ 可执行文件生成成功 (大小: $exe_size MB)"

        # 检查可执行文件大小是否合理
        if ($exe_size -lt 10) {
            Write-Warning "⚠️ 可执行文件大小 ($exe_size MB) 似乎太小，可能缺少依赖"
        }

    # 第十一步：调用 NSIS 脚本打包成安装包
    - name: 📦 打包成安装程序
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        pip install jinja2 semver
        
        $nsis_script = "$env:GITHUB_WORKSPACE/packaging/NSIS/create_windows_installer.py"
        if (-not (Test-Path $nsis_script)) {
            Write-Error "❌ NSIS脚本未找到: $nsis_script"
            Get-ChildItem -Recurse -Name "*create_windows_installer.py" | ForEach-Object { Write-Host "找到: $_" }
            exit 1
        }
        
        # 检查 PyInstaller 输出目录结构
        Write-Host "=== PyInstaller 输出目录结构 ==="
        Get-ChildItem -Recurse "$env:GITHUB_WORKSPACE/dist" | Where-Object { $_.Name -eq "UltiMaker-Cura.exe" } | ForEach-Object { Write-Host "找到可执行文件: $($_.FullName)" }

        # 确定正确的 dist_path（NSIS 脚本期望的是包含 UltiMaker-Cura 目录的父目录）
        $pyinstaller_output = "$env:GITHUB_WORKSPACE/dist/dist"
        if (-not (Test-Path "$pyinstaller_output/UltiMaker-Cura")) {
            Write-Host "尝试查找 UltiMaker-Cura 目录..."
            Get-ChildItem -Recurse -Directory -Name "UltiMaker-Cura" | ForEach-Object { Write-Host "找到目录: $_" }
            $pyinstaller_output = "$env:GITHUB_WORKSPACE/dist"
        }

        Write-Host "使用 dist_path: $pyinstaller_output"

        # 执行NSIS脚本
        python $nsis_script --source_path "$env:GITHUB_WORKSPACE" --dist_path "$pyinstaller_output" --filename "UltiMaker-Cura-$env:CURA_VERSION-Windows-x64.exe" --version $env:CURA_VERSION

        # 检查 NSIS 脚本的退出代码
        if ($LASTEXITCODE -ne 0) {
            Write-Error "❌ NSIS 脚本执行失败，退出代码: $LASTEXITCODE"
            Write-Host "检查生成的 .nsi 文件..."
            Get-ChildItem -Recurse -Name "*.nsi" | ForEach-Object { Write-Host "找到 NSI 文件: $_" }
            exit 1
        }
        
        $installer_path = "UltiMaker-Cura-$env:CURA_VERSION-Windows-x64.exe"
        if (-not (Test-Path $installer_path)) {
            Write-Error "❌ 安装包未生成: $installer_path"
            Get-ChildItem -Name
            exit 1
        }
        $size = (Get-Item $installer_path).Length/1MB
        Write-Host "✅ 安装包生成成功 (大小: $size MB)"
        
        Move-Item $installer_path "$env:GITHUB_WORKSPACE/dist/"

    # 第十二步：上传构建产物
    - name: ☁️ 上传安装包
      uses: actions/upload-artifact@v4
      with:
        name: cura-windows-installer
        path: dist/UltiMaker-Cura-${{ env.CURA_VERSION }}-Windows-x64.exe
