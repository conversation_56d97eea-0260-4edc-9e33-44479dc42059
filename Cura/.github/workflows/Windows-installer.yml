# 名称：在 GitHub Actions 页面显示的任务名称
name: Build and Package Cura Installer (Windows)

# 触发方式：手动触发 workflow（可选 future 扩展 push/tag 等触发）
on:
  workflow_dispatch:

jobs:
  build-windows:
    # 指定运行平台为 GitHub 提供的最新 Windows 系统（Windows Server 2022）
    runs-on: windows-latest

    # 设置一些环境变量
    env:
      CURA_VERSION: 5.11.0  # 指定 Cura 的版本号
      VENV_DIR: ${{ github.workspace }}\.venv  # Python 虚拟环境目录
      CONAN_USER_HOME: ${{ github.workspace }}\.conan  # Conan 本地缓存目录

    steps:
    # 第一步：检出你当前 GitHub 仓库的代码（即 wsd07/Cura）
    - name: 🧾 检出 Cura 源码
      uses: actions/checkout@v3

    # 第二步：安装 Python 3.12（Conan 2.x 需要 Python ≥ 3.8，推荐最新版）
    - name: 🐍 安装 Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    # 第三步：安装 CMake、Ninja、NSIS、7zip、Visual Studio 构建工具等依赖
    - name: 🛠 安装构建工具
      run: |
        choco install cmake --version=3.27.0 -y
        choco install ninja -y
        choco install nsis -y
        choco install 7zip -y
        choco install visualstudio2022buildtools --package-parameters "--add Microsoft.VisualStudio.Workload.VCTools" -y
        choco install windows-sdk-10.1 -y
        # 将 NSIS 添加到 PATH
        $nsisPath = "${env:ProgramFiles(x86)}\NSIS"
        echo "NSIS路径: $nsisPath"
        echo "$nsisPath" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

    # 第四步：创建虚拟环境并安装 Python 依赖，包括 Conan 2.x、sip 等
    - name: 🧪 安装 Python 依赖
      run: |
        python -m venv $env:VENV_DIR
        . "$env:VENV_DIR\Scripts\Activate.ps1"
        python -m pip install --upgrade pip
        pip install sip==6.5.1
        pip install conan==2.7.0
        pip install gitpython
        conan config install https://github.com/wsd07/conan-config.git
        conan profile detect --force

    # 第五步：克隆 Uranium 和 CuraEngine 仓库源码
    - name: 🔃 克隆 Uranium 和 CuraEngine
      run: |
        git clone https://github.com/wsd07/Uranium.git ../Uranium
        git clone https://github.com/wsd07/CuraEngine.git ../CuraEngine

    # 第六步：使用 conan editable 模式注册本地源码（新版语法）
    - name: 🔗 注册 editable 源码依赖（Conan 2.x 正确语法）
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        conan editable add ../Uranium --name=uranium --version=5.11.0 --user=wsd07 --channel=testing
        conan editable add ../CuraEngine --name=curaengine --version=5.11.0 --user=wsd07 --channel=testing

    # 第七步：创建构建目录
    - name: 🏗 创建构建目录
      run: mkdir build

    # 第八步：安装依赖并构建 Cura
    - name: 📦 安装依赖并构建 Cura
      working-directory: build
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        conan install .. --build=missing -s build_type=Release -c tools.cmake.cmaketoolchain:generator=Ninja
        conan build ..

    # 第九步：准备 PyInstaller 构建环境
    - name: 🚚 准备 pyinstaller 构建环境
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        mkdir -p dist
        cd dist
        Copy-Item "../cura_app.py" "." -Force
        Copy-Item "../resources" "." -Recurse -Force
        Copy-Item "../plugins" "." -Recurse -Force
        Copy-Item "../cura" "." -Recurse -Force
        
        # 创建简化的 PyInstaller spec 文件
        $spec = @"
        # -*- mode: python ; coding: utf-8 -*-
        block_cipher = None
        a = Analysis(['cura_app.py'], pathex=['.'], binaries=[], datas=[('resources', 'resources'), ('plugins', 'plugins'), ('cura', 'cura')], hiddenimports=['cura', 'UM', 'PyQt6', 'PyQt6.QtCore', 'PyQt6.QtWidgets', 'PyQt6.QtQml', 'PyQt6.QtQuick', 'PyQt6.QtOpenGL', 'PyQt6.QtNetwork', 'PyQt6.QtSvg', 'sip', 'numpy', 'scipy', 'trimesh', 'shapely', 'cryptography', 'keyring', 'certifi', 'requests', 'urllib3', 'charset_normalizer', 'idna'], hookspath=[], hooksconfig={}, runtime_hooks=[], excludes=[], win_no_prefer_redirects=False, win_private_assemblies=False, cipher=block_cipher, noarchive=False)
        pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)
        exe = EXE(pyz, a.scripts, [], exclude_binaries=True, name='UltiMaker-Cura', debug=False, bootloader_ignore_signals=False, strip=False, upx=True, console=False, disable_windowed_traceback=False, argv_emulation=False, target_arch=None, codesign_identity=None, entitlements_file=None)
        coll = COLLECT(exe, a.binaries, a.zipfiles, a.datas, strip=False, upx=True, upx_exclude=[], name='UltiMaker-Cura')
        "@
                $spec | Out-File -FilePath "UltiMaker-Cura.spec" -Encoding utf8
                
                Write-Host "=== 部署文件结构 ==="
                Get-ChildItem -Recurse
                
                if (-not (Test-Path "UltiMaker-Cura.spec")) {
                    Write-Error "❌ PyInstaller spec 文件未生成"
                    exit 1
                }
                Write-Host "✅ PyInstaller spec 文件已生成"

    # 第十步：安装 PyInstaller 并构建可执行文件
    - name: 🔨 使用 PyInstaller 构建可执行文件
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        pip install pyinstaller==6.3.0
        $env:PYTHONPATH = "$env:GITHUB_WORKSPACE;$env:VENV_DIR\Lib\site-packages"
        cd dist
        pyinstaller UltiMaker-Cura.spec --clean --noconfirm --log-level=INFO
        
        if (-not (Test-Path "dist/UltiMaker-Cura/UltiMaker-Cura.exe")) {
            Write-Error "❌ PyInstaller 未生成可执行文件"
            Write-Host "PyInstaller 输出目录内容："
            Get-ChildItem -Recurse
            exit 1
        }
        
        $exe_size = (Get-Item "dist/UltiMaker-Cura/UltiMaker-Cura.exe").Length/1MB
        Write-Host "✅ 可执行文件生成成功 (大小: $exe_size MB)"

    # 第十一步：调用 NSIS 脚本打包成安装包
    - name: 📦 打包成安装程序
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        pip install jinja2 semver
        
        $nsis_script = "$env:GITHUB_WORKSPACE/packaging/NSIS/create_windows_installer.py"
        if (-not (Test-Path $nsis_script)) {
            Write-Error "❌ NSIS脚本未找到: $nsis_script"
            Get-ChildItem -Recurse -Name "*create_windows_installer.py" | ForEach-Object { Write-Host "找到: $_" }
            exit 1
        }
        
        python $nsis_script --source_path "$env:GITHUB_WORKSPACE" --dist_path "$env:GITHUB_WORKSPACE/dist/dist/UltiMaker-Cura" --filename "UltiMaker-Cura-$env:CURA_VERSION-Windows-x64.exe" --version $env:CURA_VERSION
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "尝试使用备用路径..."
            python $nsis_script --source_path "$env:GITHUB_WORKSPACE" --dist_path "$env:GITHUB_WORKSPACE/dist/UltiMaker-Cura" --filename "UltiMaker-Cura-$env:CURA_VERSION-Windows-x64.exe" --version $env:CURA_VERSION
        }
        
        $installer_path = "UltiMaker-Cura-$env:CURA_VERSION-Windows-x64.exe"
        if (-not (Test-Path $installer_path)) {
            Write-Error "❌ 安装包未生成: $installer_path"
            Get-ChildItem -Name
            exit 1
        }
        $size = (Get-Item $installer_path).Length/1MB
        Write-Host "✅ 安装包生成成功 (大小: $size MB)"
        
        Move-Item $installer_path "$env:GITHUB_WORKSPACE/dist/"

    # 第十二步：上传构建产物
    - name: ☁️ 上传安装包
      uses: actions/upload-artifact@v4
      with:
        name: cura-windows-installer
        path: dist/UltiMaker-Cura-${{ env.CURA_VERSION }}-Windows-x64.exe
