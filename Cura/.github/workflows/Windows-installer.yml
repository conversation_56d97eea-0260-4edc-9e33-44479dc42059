# 名称：在 GitHub Actions 页面显示的任务名称
name: Build and Package Cura Installer (Windows)

# 触发方式：手动触发 workflow（可选 future 扩展 push/tag 等触发）
on:
  workflow_dispatch:

jobs:
  build-windows:
    # 指定运行平台为 GitHub 提供的最新 Windows 系统（Windows Server 2022）
    runs-on: windows-latest

    # 设置一些环境变量
    env:
      CURA_VERSION: 5.11.0  # 指定 Cura 的版本号
      VENV_DIR: ${{ github.workspace }}\.venv  # Python 虚拟环境目录
      CONAN_USER_HOME: ${{ github.workspace }}\.conan  # Conan 本地缓存目录

    steps:
    # 第一步：检出你当前 GitHub 仓库的代码（即 wsd07/Cura）
    - name: 🧾 检出 Cura 源码
      uses: actions/checkout@v3

    # 第二步：安装 Python 3.12（Conan 2.x 需要 Python ≥ 3.8，推荐最新版）
    - name: 🐍 安装 Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    # 第三步：安装 CMake、Ninja、NSIS、7zip、Visual Studio 构建工具等依赖
    - name: 🛠 安装构建工具
      run: |
        choco install cmake --version=3.27.0 -y  # 安装 CMake，确保版本 ≥3.23
        choco install ninja -y                  # 安装 Ninja，用于高效构建
        choco install nsis -y                   # 安装 NSIS，用于生成安装包
        choco install 7zip -y                   # 安装 7-Zip，打包或解压
        choco install visualstudio2022buildtools --package-parameters "--add Microsoft.VisualStudio.Workload.VCTools" -y
        choco install windows-sdk-10.1 -y       # 安装 Windows SDK 解决 DLL 缺失问题
        # 将 NSIS 添加到 PATH
        $nsisPath = "${env:ProgramFiles(x86)}\NSIS"
        echo "NSIS路径: $nsisPath"
        echo "$nsisPath" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

    # 第四步：创建虚拟环境并安装 Python 依赖，包括 Conan 2.x、sip 等
    - name: 🧪 安装 Python 依赖
      run: |
        python -m venv $env:VENV_DIR  # 创建虚拟环境
        . "$env:VENV_DIR\Scripts\Activate.ps1"  # 激活虚拟环境
        python -m pip install --upgrade pip
        pip install sip==6.5.1  # 安装官方要求版本的 sip
        pip install conan==2.7.0  # 安装 Conan 2.x（注意不要用旧版）
        pip install gitpython
        conan config install https://github.com/wsd07/conan-config.git  # ✅ 拉取你的共享配置
        conan profile detect --force  # ✅ 自动生成默认 profile

    # 第五步：克隆 Uranium 和 CuraEngine 仓库源码
    - name: 🔃 克隆 Uranium 和 CuraEngine
      run: |
        git clone https://github.com/wsd07/Uranium.git ../Uranium
        git clone https://github.com/wsd07/CuraEngine.git ../CuraEngine

    # 第六步：使用 conan editable 模式注册本地源码（新版语法）
    - name: 🔗 注册 editable 源码依赖（Conan 2.x 正确语法）
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        conan editable add ../Uranium --name=uranium --version=5.11.0 --user=wsd07 --channel=testing
        conan editable add ../CuraEngine --name=curaengine --version=5.11.0 --user=wsd07 --channel=testing

    # 第七步：创建构建目录
    - name: 🏗 创建构建目录
      run: mkdir build

    # 第八步：创建 Cura 包
    - name: 📦 创建 Cura 包
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        # 使用 conan create 创建完整的包（包含 install, build, package 步骤）
        conan create . --build=missing -s build_type=Release -c tools.cmake.cmaketoolchain:generator=Ninja

    # 第九步：使用 conan install 和手动调用 conanfile.py 的功能
    - name: 🚚 准备 pyinstaller 构建环境
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1

        # 创建 dist 目录
        mkdir -p dist

        # 安装依赖到本地缓存，这会触发 conanfile.py 中的各种方法
        conan install . -s build_type=Release

        # 找到创建的包的位置
        $package_ref = conan list "cura/*@wsd07/testing" --format=compact
        Write-Host "包引用: $package_ref"

        # 获取包的路径信息
        $package_info = conan list "cura/*@wsd07/testing" --format=json | ConvertFrom-Json
        Write-Host "包信息: $package_info"

        # 手动复制必要的文件（模拟 deploy() 方法的功能）
        # 复制 cura_app.py 到 dist 目录
        Copy-Item "cura_app.py" "dist/" -Force

        # 复制资源文件
        Copy-Item "resources" "dist/" -Recurse -Force
        Copy-Item "plugins" "dist/" -Recurse -Force
        Copy-Item "cura" "dist/" -Recurse -Force
        Copy-Item "packaging" "dist/" -Recurse -Force

        # 创建基于 conandata.yml 配置的完整 PyInstaller spec 文件
        echo "# -*- mode: python ; coding: utf-8 -*-" > dist/UltiMaker-Cura.spec
        echo "block_cipher = None" >> dist/UltiMaker-Cura.spec
        echo "" >> dist/UltiMaker-Cura.spec
        echo "a = Analysis(" >> dist/UltiMaker-Cura.spec
        echo "    ['cura_app.py']," >> dist/UltiMaker-Cura.spec
        echo "    pathex=['..']," >> dist/UltiMaker-Cura.spec
        echo "    binaries=[" >> dist/UltiMaker-Cura.spec
        echo "        ('CuraEngine.exe', '.')," >> dist/UltiMaker-Cura.spec
        echo "    ]," >> dist/UltiMaker-Cura.spec
        echo "    datas=[" >> dist/UltiMaker-Cura.spec
        echo "        ('plugins', 'share/cura/plugins')," >> dist/UltiMaker-Cura.spec
        echo "        ('resources', 'share/cura/resources')," >> dist/UltiMaker-Cura.spec
        echo "        ('packaging', 'packaging')," >> dist/UltiMaker-Cura.spec
        echo "    ]," >> dist/UltiMaker-Cura.spec
        echo "    hiddenimports=[" >> dist/UltiMaker-Cura.spec
        echo "        'pySavitar', 'pyArcus', 'pyDulcificum', 'pynest2d'," >> dist/UltiMaker-Cura.spec
        echo "        'PyQt6', 'PyQt6.QtNetwork', 'PyQt6.sip', 'PyQt6.Qt', 'PyQt6.Qt6'," >> dist/UltiMaker-Cura.spec
        echo "        'logging.handlers', 'zeroconf', 'fcntl', 'stl', 'serial'," >> dist/UltiMaker-Cura.spec
        echo "        'win32cred', 'win32timezone', 'pkgutil'," >> dist/UltiMaker-Cura.spec
        echo "        'cura', 'UM', 'numpy', 'scipy', 'trimesh', 'shapely'," >> dist/UltiMaker-Cura.spec
        echo "        'cryptography', 'keyring', 'certifi', 'requests'," >> dist/UltiMaker-Cura.spec
        echo "        'urllib3', 'charset_normalizer', 'idna'" >> dist/UltiMaker-Cura.spec
        echo "    ]," >> dist/UltiMaker-Cura.spec
        echo "    hookspath=[]," >> dist/UltiMaker-Cura.spec
        echo "    hooksconfig={}," >> dist/UltiMaker-Cura.spec
        echo "    runtime_hooks=[]," >> dist/UltiMaker-Cura.spec
        echo "    excludes=[]," >> dist/UltiMaker-Cura.spec
        echo "    win_no_prefer_redirects=False," >> dist/UltiMaker-Cura.spec
        echo "    win_private_assemblies=False," >> dist/UltiMaker-Cura.spec
        echo "    cipher=block_cipher," >> dist/UltiMaker-Cura.spec
        echo "    noarchive=False," >> dist/UltiMaker-Cura.spec
        echo ")" >> dist/UltiMaker-Cura.spec
        echo "" >> dist/UltiMaker-Cura.spec
        echo "pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)" >> dist/UltiMaker-Cura.spec
        echo "" >> dist/UltiMaker-Cura.spec
        echo "exe = EXE(" >> dist/UltiMaker-Cura.spec
        echo "    pyz," >> dist/UltiMaker-Cura.spec
        echo "    a.scripts," >> dist/UltiMaker-Cura.spec
        echo "    []," >> dist/UltiMaker-Cura.spec
        echo "    exclude_binaries=True," >> dist/UltiMaker-Cura.spec
        echo "    name='UltiMaker-Cura'," >> dist/UltiMaker-Cura.spec
        echo "    debug=False," >> dist/UltiMaker-Cura.spec
        echo "    bootloader_ignore_signals=False," >> dist/UltiMaker-Cura.spec
        echo "    strip=False," >> dist/UltiMaker-Cura.spec
        echo "    upx=True," >> dist/UltiMaker-Cura.spec
        echo "    console=False," >> dist/UltiMaker-Cura.spec
        echo "    disable_windowed_traceback=False," >> dist/UltiMaker-Cura.spec
        echo "    argv_emulation=False," >> dist/UltiMaker-Cura.spec
        echo "    target_arch=None," >> dist/UltiMaker-Cura.spec
        echo "    codesign_identity=None," >> dist/UltiMaker-Cura.spec
        echo "    entitlements_file=None," >> dist/UltiMaker-Cura.spec
        echo "    icon='packaging/icons/Cura.ico'" >> dist/UltiMaker-Cura.spec
        echo ")" >> dist/UltiMaker-Cura.spec
        echo "" >> dist/UltiMaker-Cura.spec
        echo "coll = COLLECT(" >> dist/UltiMaker-Cura.spec
        echo "    exe," >> dist/UltiMaker-Cura.spec
        echo "    a.binaries," >> dist/UltiMaker-Cura.spec
        echo "    a.zipfiles," >> dist/UltiMaker-Cura.spec
        echo "    a.datas," >> dist/UltiMaker-Cura.spec
        echo "    strip=False," >> dist/UltiMaker-Cura.spec
        echo "    upx=True," >> dist/UltiMaker-Cura.spec
        echo "    upx_exclude=[]," >> dist/UltiMaker-Cura.spec
        echo "    name='UltiMaker-Cura'" >> dist/UltiMaker-Cura.spec
        echo ")" >> dist/UltiMaker-Cura.spec

        # 验证文件结构
        Write-Host "=== 准备的文件结构 ==="
        Get-ChildItem -Recurse dist

        # 检查 PyInstaller spec 文件
        if (-not (Test-Path "dist/UltiMaker-Cura.spec")) {
            Write-Error "❌ PyInstaller spec 文件未生成"
            exit 1
        }
        Write-Host "✅ PyInstaller 构建环境准备完成"

    # 第十步：安装 PyInstaller 并构建可执行文件
    - name: 🔨 使用 PyInstaller 构建可执行文件
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        pip install pyinstaller==6.3.0

        # 设置 PYTHONPATH 以包含当前目录和 venv 的 site-packages
        $env:PYTHONPATH = "$env:GITHUB_WORKSPACE;$env:VENV_DIR\Lib\site-packages"

        # 切换到部署目录并运行 pyinstaller（使用准备好的 spec 文件）
        cd dist
        pyinstaller UltiMaker-Cura.spec --clean --noconfirm --log-level=INFO

        # 验证可执行文件生成
        if (-not (Test-Path "dist/UltiMaker-Cura/UltiMaker-Cura.exe")) {
            Write-Error "❌ PyInstaller 未生成可执行文件"
            Write-Host "PyInstaller 输出目录内容："
            Get-ChildItem -Recurse
            exit 1
        }

        $exe_size = (Get-Item "dist/UltiMaker-Cura/UltiMaker-Cura.exe").Length/1MB
        Write-Host "✅ 可执行文件生成成功 (大小: $exe_size MB)"

    # 第十一步：调用 NSIS 脚本打包成安装包
    - name: 📦 打包成安装程序
      run: |
        . $env:VENV_DIR\Scripts\activate.ps1
        pip install jinja2 semver
        
        $nsis_script = "$env:GITHUB_WORKSPACE/packaging/NSIS/create_windows_installer.py"
        if (-not (Test-Path $nsis_script)) {
            Write-Error "❌ NSIS脚本未找到: $nsis_script"
            Get-ChildItem -Recurse -Name "*create_windows_installer.py" | ForEach-Object { Write-Host "找到: $_" }
            exit 1
        }
        
        python $nsis_script --source_path "$env:GITHUB_WORKSPACE" --dist_path "$env:GITHUB_WORKSPACE/dist/dist/UltiMaker-Cura" --filename "UltiMaker-Cura-$env:CURA_VERSION-Windows-x64.exe" --version $env:CURA_VERSION
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "尝试使用备用路径..."
            python $nsis_script --source_path "$env:GITHUB_WORKSPACE" --dist_path "$env:GITHUB_WORKSPACE/dist/UltiMaker-Cura" --filename "UltiMaker-Cura-$env:CURA_VERSION-Windows-x64.exe" --version $env:CURA_VERSION
        }
        
        $installer_path = "UltiMaker-Cura-$env:CURA_VERSION-Windows-x64.exe"
        if (-not (Test-Path $installer_path)) {
            Write-Error "❌ 安装包未生成: $installer_path"
            Get-ChildItem -Name
            exit 1
        }
        $size = (Get-Item $installer_path).Length/1MB
        Write-Host "✅ 安装包生成成功 (大小: $size MB)"
        
        Move-Item $installer_path "$env:GITHUB_WORKSPACE/dist/"

    # 第十二步：上传构建产物
    - name: ☁️ 上传安装包
      uses: actions/upload-artifact@v4
      with:
        name: cura-windows-installer
        path: dist/UltiMaker-Cura-${{ env.CURA_VERSION }}-Windows-x64.exe
